<% content_for :title, "Bundle Builder - #{@bundle['name']}" %>

<div class="bundle-builder-container">
  <!-- Header -->
  <div class="builder-header">
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <h2 class="mb-1">
          <span class="me-2">🏗️</span>Bundle Builder
        </h2>
        <p class="text-muted mb-0">
          Building: <strong><%= @bundle['name'] %></strong> (<%= @bundle['sku'] %>)
        </p>
      </div>
      <div>
        <%= link_to "← Back to Bundles", admin_bundles_path, class: "btn btn-outline-secondary me-2" %>
        <button type="button" class="btn btn-success" id="saveBundleBtn">
          <span class="me-1">💾</span>Save Bundle
        </button>
      </div>
    </div>
  </div>

  <!-- Main Builder Interface -->
  <%= form_with url: admin_bundle_builder_path, method: :post, local: true, id: "bundleBuilderForm" do |form| %>
    <div class="row">
      <!-- Left Panel - Categories Builder -->
      <div class="col-lg-8">
        <div class="builder-panel">
          <div class="panel-header">
            <h4 class="mb-0">
              <span class="me-2">📂</span>Categories
              <small class="text-muted">(Drag to reorder)</small>
            </h4>
            <button type="button" class="btn btn-primary btn-sm" id="addCategoryBtn">
              <span class="me-1">+</span>Add Category
            </button>
          </div>
          
          <div class="categories-container" id="categoriesContainer">
            <!-- Categories will be dynamically added here -->
            <div class="empty-state" id="emptyCategoriesState">
              <div class="text-center py-5">
                <div class="mb-3" style="font-size: 48px; opacity: 0.3;">📂</div>
                <h5>No Categories Yet</h5>
                <p class="text-muted">Add your first category to start building your bundle</p>
                <button type="button" class="btn btn-primary" onclick="addCategory()">
                  <span class="me-1">+</span>Add First Category
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Panel - Products & Preview -->
      <div class="col-lg-4">
        <!-- Available Products -->
        <div class="builder-panel mb-4">
          <div class="panel-header">
            <h5 class="mb-0">
              <span class="me-2">🛍️</span>Available Products
            </h5>
          </div>
          
          <div class="products-list" id="availableProducts">
            <% @available_products.each do |product| %>
              <div class="product-item" data-product-id="<%= product['id'] %>" draggable="true">
                <div class="product-info">
                  <div class="product-name"><%= product['name'] %></div>
                  <div class="product-details">
                    <span class="product-sku"><%= product['sku'] %></span>
                    <span class="product-price">$<%= product['price'] %></span>
                  </div>
                </div>
                <div class="drag-handle">⋮⋮</div>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Bundle Preview -->
        <div class="builder-panel">
          <div class="panel-header">
            <h5 class="mb-0">
              <span class="me-2">👁️</span>Bundle Preview
            </h5>
          </div>
          
          <div class="bundle-preview" id="bundlePreview">
            <div class="preview-header">
              <h6><%= @bundle['name'] %></h6>
              <small class="text-muted"><%= @bundle['sku'] %></small>
            </div>
            
            <div class="preview-description">
              <%= @bundle['description'] %>
            </div>
            
            <div class="preview-categories" id="previewCategories">
              <small class="text-muted">No categories configured</small>
            </div>
            
            <div class="preview-stats mt-3">
              <div class="stat">
                <span class="stat-label">Categories:</span>
                <span class="stat-value" id="categoriesCount">0</span>
              </div>
              <div class="stat">
                <span class="stat-label">Total Products:</span>
                <span class="stat-value" id="productsCount">0</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  <% end %>
</div>

<!-- Category Template (Hidden) -->
<template id="categoryTemplate">
  <div class="category-card" data-category-index="">
    <div class="category-header">
      <div class="category-drag-handle">⋮⋮</div>
      <input type="text" class="category-name-input" placeholder="Category Name" required>
      <div class="category-actions">
        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeCategory(this)">
          🗑️
        </button>
      </div>
    </div>
    
    <div class="category-settings">
      <div class="row">
        <div class="col-6">
          <label class="form-check">
            <input type="checkbox" class="form-check-input category-required">
            <span class="form-check-label">Required</span>
          </label>
        </div>
        <div class="col-6">
          <label class="form-label">Max Selections:</label>
          <input type="number" class="form-control form-control-sm category-max-selections" value="1" min="1">
        </div>
      </div>
    </div>
    
    <div class="category-products" data-category-products="">
      <div class="products-drop-zone">
        <p class="text-muted mb-0">Drag products here</p>
      </div>
    </div>
  </div>
</template>

<style>
  .bundle-builder-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
  }
  
  .builder-header {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .builder-panel {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .panel-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
    display: flex;
    justify-content: between;
    align-items: center;
  }
  
  .categories-container {
    padding: 20px;
    min-height: 400px;
  }
  
  .category-card {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.2s ease;
  }
  
  .category-card:hover {
    border-color: #667eea;
    background: #f0f4ff;
  }
  
  .category-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
  }
  
  .category-drag-handle {
    cursor: grab;
    color: #6c757d;
    font-size: 18px;
  }
  
  .category-name-input {
    flex: 1;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 8px 12px;
    font-weight: 500;
  }
  
  .category-settings {
    margin-bottom: 15px;
  }
  
  .products-drop-zone {
    border: 2px dashed #ced4da;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .products-drop-zone.drag-over {
    border-color: #667eea;
    background: #f0f4ff;
  }
  
  .product-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
    display: flex;
    justify-content: between;
    align-items: center;
    cursor: grab;
    transition: all 0.2s ease;
  }
  
  .product-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .product-info {
    flex: 1;
  }
  
  .product-name {
    font-weight: 500;
    color: #495057;
  }
  
  .product-details {
    font-size: 12px;
    color: #6c757d;
    display: flex;
    gap: 10px;
  }
  
  .product-sku {
    font-family: monospace;
    background: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
  }
  
  .drag-handle {
    color: #6c757d;
    cursor: grab;
  }
  
  .bundle-preview {
    padding: 20px;
  }
  
  .preview-header h6 {
    margin-bottom: 4px;
    color: #495057;
  }
  
  .preview-description {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 15px;
    line-height: 1.4;
  }
  
  .stat {
    display: flex;
    justify-content: between;
    margin-bottom: 5px;
  }
  
  .stat-label {
    font-size: 13px;
    color: #6c757d;
  }
  
  .stat-value {
    font-weight: 500;
    color: #495057;
  }

  /* Drag and Drop Enhancements */
  .product-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
  }

  .category-product {
    background: #e8f5e8 !important;
    border-color: #28a745 !important;
    position: relative;
  }

  .category-product .btn-outline-danger {
    position: absolute;
    top: 5px;
    right: 5px;
    padding: 2px 6px;
    font-size: 12px;
    line-height: 1;
  }

  .preview-category {
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #667eea;
  }

  .form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
  }

  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  console.log('Bundle Builder loaded for:', '<%= @bundle['name'] %>');

  let categoryCounter = 0;
  let draggedProduct = null;

  // Initialize builder
  initializeBundleBuilder();

  function initializeBundleBuilder() {
    // Add event listeners
    document.getElementById('addCategoryBtn').addEventListener('click', addCategory);
    document.getElementById('saveBundleBtn').addEventListener('click', saveBundle);

    // Initialize drag and drop for products
    initializeProductDragDrop();

    // Update preview initially
    updateBundlePreview();
  }

  function addCategory() {
    const container = document.getElementById('categoriesContainer');
    const emptyState = document.getElementById('emptyCategoriesState');
    const template = document.getElementById('categoryTemplate');

    // Hide empty state
    if (emptyState) {
      emptyState.style.display = 'none';
    }

    // Clone template
    const categoryElement = template.content.cloneNode(true);
    const categoryCard = categoryElement.querySelector('.category-card');

    // Set unique index
    categoryCounter++;
    categoryCard.setAttribute('data-category-index', categoryCounter);

    // Set default name
    const nameInput = categoryCard.querySelector('.category-name-input');
    nameInput.value = `Category ${categoryCounter}`;
    nameInput.setAttribute('name', `categories[${categoryCounter}][name]`);

    // Set form field names
    const requiredCheckbox = categoryCard.querySelector('.category-required');
    requiredCheckbox.setAttribute('name', `categories[${categoryCounter}][required]`);

    const maxSelectionsInput = categoryCard.querySelector('.category-max-selections');
    maxSelectionsInput.setAttribute('name', `categories[${categoryCounter}][max_selections]`);

    // Add event listeners
    nameInput.addEventListener('input', updateBundlePreview);
    requiredCheckbox.addEventListener('change', updateBundlePreview);
    maxSelectionsInput.addEventListener('change', updateBundlePreview);

    // Initialize drop zone
    initializeCategoryDropZone(categoryCard);

    // Add to container
    container.appendChild(categoryElement);

    // Update preview
    updateBundlePreview();

    // Focus on name input
    setTimeout(() => nameInput.focus(), 100);
  }

  function removeCategory(button) {
    const categoryCard = button.closest('.category-card');
    const container = document.getElementById('categoriesContainer');

    // Confirm deletion
    const categoryName = categoryCard.querySelector('.category-name-input').value || 'Unnamed Category';
    if (!confirm(`Are you sure you want to remove "${categoryName}"?`)) {
      return;
    }

    // Remove category
    categoryCard.remove();

    // Show empty state if no categories
    const remainingCategories = container.querySelectorAll('.category-card');
    if (remainingCategories.length === 0) {
      document.getElementById('emptyCategoriesState').style.display = 'block';
    }

    // Update preview
    updateBundlePreview();
  }

  function initializeProductDragDrop() {
    const products = document.querySelectorAll('.product-item');

    products.forEach(product => {
      product.addEventListener('dragstart', function(e) {
        draggedProduct = this;
        this.style.opacity = '0.5';
        e.dataTransfer.effectAllowed = 'copy';
      });

      product.addEventListener('dragend', function(e) {
        this.style.opacity = '1';
        draggedProduct = null;
      });
    });
  }

  function initializeCategoryDropZone(categoryCard) {
    const dropZone = categoryCard.querySelector('.products-drop-zone');

    dropZone.addEventListener('dragover', function(e) {
      e.preventDefault();
      e.dataTransfer.dropEffect = 'copy';
      this.classList.add('drag-over');
    });

    dropZone.addEventListener('dragleave', function(e) {
      this.classList.remove('drag-over');
    });

    dropZone.addEventListener('drop', function(e) {
      e.preventDefault();
      this.classList.remove('drag-over');

      if (draggedProduct) {
        addProductToCategory(draggedProduct, categoryCard);
      }
    });
  }

  function addProductToCategory(productElement, categoryCard) {
    const dropZone = categoryCard.querySelector('.products-drop-zone');
    const categoryIndex = categoryCard.getAttribute('data-category-index');

    // Clone product for category
    const productClone = productElement.cloneNode(true);
    productClone.classList.add('category-product');

    // Add remove button
    const removeBtn = document.createElement('button');
    removeBtn.type = 'button';
    removeBtn.className = 'btn btn-sm btn-outline-danger ms-2';
    removeBtn.innerHTML = '×';
    removeBtn.onclick = function() {
      productClone.remove();
      updateBundlePreview();
      checkDropZoneEmpty(categoryCard);
    };

    productClone.appendChild(removeBtn);

    // Add hidden input for form submission
    const productId = productElement.getAttribute('data-product-id');
    const hiddenInput = document.createElement('input');
    hiddenInput.type = 'hidden';
    hiddenInput.name = `categories[${categoryIndex}][products][]`;
    hiddenInput.value = productId;
    productClone.appendChild(hiddenInput);

    // Replace drop zone content or add to existing products
    if (dropZone.querySelector('p')) {
      dropZone.innerHTML = '';
    }

    dropZone.appendChild(productClone);
    updateBundlePreview();
  }

  function checkDropZoneEmpty(categoryCard) {
    const dropZone = categoryCard.querySelector('.products-drop-zone');
    if (dropZone.children.length === 0) {
      dropZone.innerHTML = '<p class="text-muted mb-0">Drag products here</p>';
    }
  }

  function updateBundlePreview() {
    const categories = document.querySelectorAll('.category-card');
    const previewCategories = document.getElementById('previewCategories');
    const categoriesCount = document.getElementById('categoriesCount');
    const productsCount = document.getElementById('productsCount');

    let totalProducts = 0;
    let previewHTML = '';

    if (categories.length === 0) {
      previewHTML = '<small class="text-muted">No categories configured</small>';
    } else {
      categories.forEach(category => {
        const name = category.querySelector('.category-name-input').value || 'Unnamed';
        const required = category.querySelector('.category-required').checked;
        const maxSelections = category.querySelector('.category-max-selections').value;
        const products = category.querySelectorAll('.category-product');

        totalProducts += products.length;

        previewHTML += `
          <div class="preview-category mb-2">
            <div class="d-flex justify-content-between align-items-center">
              <span class="fw-medium">${name}</span>
              <small class="text-muted">
                ${required ? 'Required' : 'Optional'} • Max: ${maxSelections} • ${products.length} products
              </small>
            </div>
          </div>
        `;
      });
    }

    previewCategories.innerHTML = previewHTML;
    categoriesCount.textContent = categories.length;
    productsCount.textContent = totalProducts;
  }

  function saveBundle() {
    const form = document.getElementById('bundleBuilderForm');
    const categories = document.querySelectorAll('.category-card');

    // Validate categories
    if (categories.length === 0) {
      alert('Please add at least one category before saving.');
      return;
    }

    // Validate category names
    let hasEmptyNames = false;
    categories.forEach(category => {
      const nameInput = category.querySelector('.category-name-input');
      if (!nameInput.value.trim()) {
        hasEmptyNames = true;
        nameInput.focus();
        nameInput.classList.add('is-invalid');
      } else {
        nameInput.classList.remove('is-invalid');
      }
    });

    if (hasEmptyNames) {
      alert('Please provide names for all categories.');
      return;
    }

    // Show loading state
    const saveBtn = document.getElementById('saveBundleBtn');
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Saving...';
    saveBtn.disabled = true;

    // Submit form
    form.submit();
  }

  // Make functions globally available
  window.addCategory = addCategory;
  window.removeCategory = removeCategory;
});
</script>
