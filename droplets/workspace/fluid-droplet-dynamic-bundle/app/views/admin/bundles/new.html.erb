<% content_for :title, "Create New Bundle" %>

<div class="d-flex justify-content-between align-items-center mb-4">
  <h2>Create New Dynamic Bundle</h2>
  <%= link_to "← Back to Bundles", admin_bundles_path, class: "btn btn-secondary" %>
</div>

<div class="row">
  <div class="col-md-8">
    <%= form_with model: [:admin, @bundle], local: true, html: { class: "bundle-form" } do |form| %>

      <!-- Error Messages -->
      <% if @bundle.errors.any? %>
        <div class="alert alert-danger mb-4">
          <h6 class="alert-heading mb-2">
            <i class="fas fa-exclamation-triangle me-2"></i>Please fix the following errors:
          </h6>
          <ul class="mb-0">
            <% @bundle.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>

      <div class="form-group">
        <%= form.label :name, class: "form-label" %>
        <%= form.text_field :name,
            class: "form-control #{'is-invalid' if @bundle.errors[:name].any?}",
            placeholder: "e.g., Transformation Bundle",
            required: true %>
        <% if @bundle.errors[:name].any? %>
          <div class="invalid-feedback d-block">
            <%= @bundle.errors[:name].first %>
          </div>
        <% end %>
        <div class="form-text">
          This will be the display name for your bundle that customers will see.
        </div>
      </div>

      <div class="form-group">
        <%= form.label :sku, "SKU", class: "form-label" %>
        <%= form.text_field :sku,
            class: "form-control #{'is-invalid' if @bundle.errors[:sku].any?}",
            placeholder: "Will be auto-generated from name",
            required: true %>
        <% if @bundle.errors[:sku].any? %>
          <div class="invalid-feedback d-block">
            <%= @bundle.errors[:sku].first %>
          </div>
        <% end %>
        <div class="form-text">
          Unique identifier for this bundle. Use uppercase letters, numbers, and hyphens only.
        </div>
      </div>

      <div class="form-group">
        <%= form.label :description, class: "form-label" %>
        <%= form.text_area :description,
            class: "form-control #{'is-invalid' if @bundle.errors[:description].any?}",
            rows: 4,
            placeholder: "Describe what this bundle contains and its benefits..." %>
        <% if @bundle.errors[:description].any? %>
          <div class="invalid-feedback d-block">
            <%= @bundle.errors[:description].first %>
          </div>
        <% end %>
        <div class="form-text">
          Optional description that will help customers understand this bundle (max 500 characters).
        </div>
      </div>

      <div class="form-actions">
        <%= form.submit "Create Bundle", class: "btn btn-primary btn-lg" %>
        <%= link_to "Cancel", admin_bundles_path, class: "btn btn-secondary" %>
      </div>
    <% end %>
  </div>
  
  <div class="col-md-4">
    <div class="info-panel">
      <h4>📦 What is a Dynamic Bundle?</h4>
      <p>
        A dynamic bundle is a configurable product that allows customers to select items 
        from predefined categories while maintaining bundle-level pricing.
      </p>
      
      <h5>Next Steps:</h5>
      <ol>
        <li><strong>Create the bundle</strong> - Set up the basic information</li>
        <li><strong>Add categories</strong> - Define selectable product groups</li>
        <li><strong>Assign products</strong> - Add products to each category</li>
        <li><strong>Configure metadata</strong> - Export configuration to Fluid</li>
      </ol>
      
      <div class="example-box">
        <h6>Example Bundle Structure:</h6>
        <ul class="example-list">
          <li>🥤 <strong>Protein Powders</strong> (Select 2)
            <ul>
              <li>Whey Protein - Vanilla</li>
              <li>Whey Protein - Chocolate</li>
              <li>Plant Protein - Berry</li>
            </ul>
          </li>
          <li>⚡ <strong>Pre-Workout</strong> (Select 1)
            <ul>
              <li>Energy Boost - Citrus</li>
              <li>Focus Formula</li>
            </ul>
          </li>
          <li>🏃 <strong>Recovery</strong> (Select 1)
            <ul>
              <li>BCAA Complex</li>
              <li>Recovery Plus</li>
            </ul>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>

<style>
  .row {
    display: flex;
    gap: 30px;
  }
  
  .col-md-8 {
    flex: 0 0 66.666667%;
  }
  
  .col-md-4 {
    flex: 0 0 33.333333%;
  }
  
  .bundle-form {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
  }
  
  .form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 12px;
  }
  
  .info-panel {
    background: #fff;
    padding: 24px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    position: sticky;
    top: 20px;
  }
  
  .info-panel h4 {
    color: #495057;
    margin-bottom: 12px;
    font-size: 16px;
  }
  
  .info-panel h5 {
    color: #495057;
    margin: 20px 0 10px 0;
    font-size: 14px;
  }
  
  .info-panel h6 {
    color: #495057;
    margin: 16px 0 8px 0;
    font-size: 13px;
  }
  
  .info-panel p {
    font-size: 14px;
    color: #6c757d;
    line-height: 1.5;
  }
  
  .info-panel ol {
    font-size: 14px;
    color: #6c757d;
    padding-left: 20px;
  }
  
  .info-panel ol li {
    margin-bottom: 6px;
  }
  
  .example-box {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    margin-top: 16px;
    border-left: 4px solid #007bff;
  }
  
  .example-list {
    font-size: 13px;
    color: #495057;
    margin: 8px 0 0 0;
    padding-left: 0;
    list-style: none;
  }
  
  .example-list > li {
    margin-bottom: 8px;
  }
  
  .example-list ul {
    margin: 4px 0 0 20px;
    padding-left: 0;
    list-style: none;
  }
  
  .example-list ul li {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 2px;
  }
  
  .example-list ul li:before {
    content: "• ";
    color: #007bff;
    margin-right: 6px;
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const nameField = document.querySelector('input[name="bundle[name]"]');
  const skuField = document.querySelector('input[name="bundle[sku]"]');
  
  // Auto-generate SKU from name (matching Ruby logic)
  if (nameField && skuField) {
    nameField.addEventListener('input', function() {
      if (skuField.value === '' || skuField.dataset.autoGenerated === 'true') {
        const sku = this.value
          .toUpperCase()
          .replace(/[^A-Z0-9\s]/g, '')
          .replace(/\s+/g, '-')
          .substring(0, 17); // Leave room for -001 suffix

        skuField.value = sku ? `${sku}-001` : '';
        skuField.dataset.autoGenerated = 'true';
      }
    });

    // Mark as manually edited if user types in SKU field
    skuField.addEventListener('input', function() {
      this.dataset.autoGenerated = 'false';
    });
  }
  
  // Form validation
  const form = document.querySelector('.bundle-form');
  if (form) {
    form.addEventListener('submit', function(e) {
      const name = nameField.value.trim();
      const sku = skuField.value.trim();
      
      if (!name) {
        alert('Please enter a bundle name.');
        nameField.focus();
        e.preventDefault();
        return false;
      }
      
      if (!sku) {
        alert('Please enter a bundle SKU.');
        skuField.focus();
        e.preventDefault();
        return false;
      }
      
      if (!/^[A-Z0-9\-]+$/.test(sku)) {
        alert('SKU can only contain uppercase letters, numbers, and hyphens.');
        skuField.focus();
        e.preventDefault();
        return false;
      }
    });
  }
});
</script>
