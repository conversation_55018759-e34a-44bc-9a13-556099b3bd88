# frozen_string_literal: true

# Bundle model for managing dynamic product bundles
# This model handles validation and business logic for bundles
class Bundle
  include ActiveModel::Model
  include ActiveModel::Attributes
  include ActiveModel::Validations

  # Bundle attributes
  attribute :id, :integer
  attribute :name, :string
  attribute :sku, :string
  attribute :description, :string
  attribute :status, :string, default: 'draft'
  attribute :created_at, :datetime
  attribute :updated_at, :datetime
  attribute :categories, :array, default: []

  # Validations
  validates :name, presence: true, length: { minimum: 3, maximum: 100 }
  validates :sku, presence: true, length: { minimum: 3, maximum: 50 }
  validates :sku, format: { 
    with: /\A[A-Z0-9\-]+\z/, 
    message: "can only contain uppercase letters, numbers, and hyphens" 
  }
  validates :status, inclusion: { in: %w[draft active inactive] }
  validates :description, length: { maximum: 500 }

  # Custom validation for SKU uniqueness (would need to check against Fluid API)
  validate :sku_uniqueness

  # Status helpers
  def draft?
    status == 'draft'
  end

  def active?
    status == 'active'
  end

  def inactive?
    status == 'inactive'
  end

  # Generate SKU from name
  def self.generate_sku_from_name(name)
    return '' if name.blank?
    
    base_sku = name
      .upcase
      .gsub(/[^A-Z0-9\s]/, '')
      .gsub(/\s+/, '-')
      .slice(0, 17) # Leave room for -001 suffix
    
    "#{base_sku}-001"
  end

  # Check if bundle has categories configured
  def has_categories?
    categories.present? && categories.any?
  end

  # Get category count
  def category_count
    categories&.size || 0
  end

  # Simulate persistence methods for compatibility with Rails forms
  def persisted?
    id.present?
  end

  def new_record?
    !persisted?
  end

  def to_param
    id.to_s if id
  end

  private

  def sku_uniqueness
    # This would need to be implemented to check against Fluid API
    # For now, we'll just ensure it's not empty after validation
    return unless sku.present?
    
    # TODO: Implement actual uniqueness check against Fluid API
    # For development, we'll allow any valid SKU format
  end
end
